/**
 * Woo Product Manager - Course Info Tab Styles
 * Kurs bilgileri sekmesi için CSS stilleri
 */

.woo-pruduct-course-info {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
}

.woo-pruduct-course-info h3 {
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 18px;
}

.curriculum-summary {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    margin-left: 10px;
}

/* Eğitmen Bilgileri */
.instructor-card {
    display: flex;
    gap: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.instructor-avatar {
    flex-shrink: 0;
}

.instructor-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #0073aa;
}

.instructor-details {
    flex: 1;
}

.instructor-header {
    margin-bottom: 15px;
}

.instructor-name {
    margin: 0 0 5px 0;
    font-size: 20px;
}

.instructor-name a {
    color: #0073aa;
    text-decoration: none;
}

.instructor-name a:hover {
    text-decoration: underline;
}

.instructor-title {
    margin: 0;
    color: #666;
    font-style: italic;
    font-size: 14px;
}

.instructor-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.instructor-stats .stat-item {
    text-align: center;
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    min-width: 60px;
}

.instructor-stats .stat-number {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 2px;
}

.instructor-stats .stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.instructor-bio {
    margin-bottom: 15px;
}

.instructor-bio p {
    margin: 0;
    color: #555;
    line-height: 1.5;
}

.instructor-social {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f0f0f0;
    border-radius: 50%;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #0073aa;
    color: #fff;
    text-decoration: none;
}

.social-link .dashicons {
    font-size: 16px;
}

.instructor-profile-btn {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.instructor-profile-btn:hover {
    background: #005a87;
    color: #fff;
    text-decoration: none;
}

.course-overview,
.course-meta-info,
.course-instructor-info,
.course-curriculum,
.course-requirements,
.course-target-audience,
.course-benefits {
    margin-bottom: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

.meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
}

.meta-label {
    font-weight: 600;
    color: #666;
}

.meta-value {
    color: #333;
}

/* Kayıtlı öğrenci sayısı ve güncellenme tarihi yan yana */
.course-meta-info .meta-grid .student-update-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    grid-column: 1 / -1;
}

/* Güncellenme tarihi özel stilleri */
.meta-item.updated-date {
    border-left-color: #28a745;
}

.meta-item.updated-date .meta-label {
    color: #28a745;
}

.meta-item.updated-date .meta-value {
    font-weight: 600;
    color: #155724;
}

/* Güncellenme tarihi ikonu */
.meta-item.updated-date .meta-label::before {
    content: "🕒 ";
    margin-right: 4px;
}

.curriculum-accordion {
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.curriculum-topic {
    border-bottom: 1px solid #ddd;
}

.curriculum-topic:last-child {
    border-bottom: none;
}

.topic-header {
    background: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
    user-select: none;
    border-bottom: 1px solid #e9ecef;
}

.topic-header:hover {
    background: #f8f9fa;
}

.topic-header.active {
    background: #f8f9fa;
    color: #333;
}

.topic-title {
    margin: 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    flex: 1;
}

.topic-count {
    margin-left: 10px;
    font-size: 14px;
    opacity: 0.8;
    font-weight: normal;
}

.accordion-toggle {
    transition: transform 0.3s ease;
}

.accordion-toggle .dashicons {
    font-size: 20px;
}

.topic-header.active .accordion-toggle {
    transform: rotate(180deg);
}

.topic-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #fff;
}

.topic-content.active {
    max-height: 1000px; /* Yeterince büyük bir değer */
}

.topic-lessons,
.topic-quizzes {
    list-style: none;
    margin: 0;
    padding: 0;
}

.lesson-item,
.quiz-item {
    padding: 15px 25px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.lesson-item:hover,
.quiz-item:hover {
    background: #f8f9fa;
}

.lesson-item:last-child,
.quiz-item:last-child {
    border-bottom: none;
}

.lesson-item .dashicons,
.quiz-item .dashicons {
    margin-right: 12px;
    color: #0073aa;
    font-size: 18px;
    flex-shrink: 0;
}

.item-title {
    flex: 1;
    font-weight: 500;
}

.item-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 10px;
}

.quiz-item {
    background: #fff8e1;
}

.quiz-item .dashicons {
    color: #ff9800;
}

.quiz-item .item-type {
    background: #fff3e0;
    color: #f57c00;
}

/* Responsive tasarım */
@media (max-width: 768px) {
    .meta-grid {
        grid-template-columns: 1fr;
    }

    .instructor-card {
        flex-direction: column;
        text-align: center;
    }

    .instructor-stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .instructor-social {
        justify-content: center;
    }
    
    .course-meta-info .meta-grid .student-update-row {
        grid-template-columns: 1fr;
    }
}
