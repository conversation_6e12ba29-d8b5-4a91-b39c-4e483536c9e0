/**
 * Woo Product Manager Frontend Styles
 * Kurs etiketleri ve diğer frontend stilleri
 */

/* <PERSON><PERSON><PERSON><PERSON> kartında kurs etiketi (shop loop) */
.woo-pruduct-course-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.woo-pruduct-course-badge .course-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.woo-pruduct-course-badge .course-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> say<PERSON>ında kurs etiketi */
.woo-pruduct-course-badge-single {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
}

.woo-pruduct-course-badge-single .course-badge-single {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: inline-block;
}

.woo-pruduct-course-badge-single .course-badge-single:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.5);
}

/* Ürün kartı container'ının relative pozisyonu için */
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
    position: relative;
}

/* Tek ürün sayfası için */
.woocommerce div.product div.images,
.woocommerce-page div.product div.images {
    position: relative;
}

/* Responsive tasarım */
@media (max-width: 768px) {
    .woo-pruduct-course-badge {
        top: 8px;
        right: 8px;
    }
    
    .woo-pruduct-course-badge .course-badge {
        padding: 4px 8px;
        font-size: 10px;
    }
    
    .woo-pruduct-course-badge-single {
        top: 10px;
        right: 10px;
    }
    
    .woo-pruduct-course-badge-single .course-badge-single {
        padding: 6px 12px;
        font-size: 11px;
    }
}

/* Alternatif renkler */
.woo-pruduct-course-badge.badge-green .course-badge {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    box-shadow: 0 2px 8px rgba(17, 153, 142, 0.3);
}

.woo-pruduct-course-badge.badge-orange .course-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
}

.woo-pruduct-course-badge.badge-blue .course-badge {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

/* Animasyon efektleri */
@keyframes badgePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.woo-pruduct-course-badge .course-badge,
.woo-pruduct-course-badge-single .course-badge-single {
    animation: badgePulse 2s infinite;
}

/* Hover durumunda animasyonu durdur */
.woo-pruduct-course-badge:hover .course-badge,
.woo-pruduct-course-badge-single:hover .course-badge-single {
    animation: none;
}

/* Dark mode desteği */
@media (prefers-color-scheme: dark) {
    .woo-pruduct-course-badge .course-badge,
    .woo-pruduct-course-badge-single .course-badge-single {
        border-color: rgba(255, 255, 255, 0.4);
    }
}

/* Kurs bilgileri sekmesi güncellenme tarihi stilleri */
.woo-pruduct-course-info .meta-item.updated-date {
    border-left-color: #28a745;
}

.woo-pruduct-course-info .meta-item.updated-date .meta-label {
    color: #28a745;
}

.woo-pruduct-course-info .meta-item.updated-date .meta-value {
    font-weight: 600;
    color: #155724;
}

/* Güncellenme tarihi ikonu */
.woo-pruduct-course-info .meta-item.updated-date .meta-label::before {
    content: "🕒 ";
    margin-right: 4px;
}

/* Kayıtlı öğrenci sayısı ve güncellenme tarihi yan yana */
.course-meta-info .meta-grid .student-update-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    grid-column: 1 / -1;
}

@media (max-width: 600px) {
    .course-meta-info .meta-grid .student-update-row {
        grid-template-columns: 1fr;
    }
}
