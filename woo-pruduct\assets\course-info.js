/**
 * Woo Product Manager - Course Info Tab JavaScript
 * Kurs bilgileri sekmesi için JavaScript işlevleri
 */

jQuery(document).ready(function($) {
    // Accordion işlevselliği
    $('.topic-header').on('click', function() {
        const $header = $(this);
        const $content = $header.next('.topic-content');
        const $accordion = $header.closest('.curriculum-accordion');

        // Diğer açık olan konuları kapat
        $accordion.find('.topic-header.active').not($header).each(function() {
            $(this).removeClass('active');
            $(this).next('.topic-content').removeClass('active');
        });

        // Mevcut konuyu aç/kapat
        $header.toggleClass('active');
        $content.toggleClass('active');

        // Smooth scroll etkisi için
        if ($header.hasClass('active')) {
            setTimeout(function() {
                $('html, body').animate({
                    scrollTop: $header.offset().top - 100
                }, 300);
            }, 150);
        }
    });

    // İlk konuyu varsayılan olarak aç
    $('.curriculum-accordion .topic-header:first').addClass('active');
    $('.curriculum-accordion .topic-content:first').addClass('active');

    // Klavye erişilebilirliği
    $('.topic-header').on('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            $(this).click();
        }
    });

    // ARIA attributes ekle
    $('.topic-header').each(function(index) {
        const $header = $(this);
        const $content = $header.next('.topic-content');
        const headerId = 'topic-header-' + index;
        const contentId = 'topic-content-' + index;

        $header.attr({
            'id': headerId,
            'role': 'button',
            'tabindex': '0',
            'aria-expanded': $header.hasClass('active') ? 'true' : 'false',
            'aria-controls': contentId
        });

        $content.attr({
            'id': contentId,
            'role': 'region',
            'aria-labelledby': headerId
        });
    });

    // Accordion durumu değiştiğinde ARIA güncelle
    $('.topic-header').on('click', function() {
        setTimeout(function() {
            $('.topic-header').each(function() {
                const isActive = $(this).hasClass('active');
                $(this).attr('aria-expanded', isActive ? 'true' : 'false');
            });
        }, 50);
    });

    // Kurs etiketi animasyonları
    $('.woo-pruduct-course-badge .course-badge, .woo-pruduct-course-badge-single .course-badge-single').hover(
        function() {
            $(this).css('animation', 'none');
        },
        function() {
            $(this).css('animation', 'badgePulse 2s infinite');
        }
    );

    // Responsive accordion davranışı
    function handleResponsiveAccordion() {
        if ($(window).width() <= 768) {
            $('.topic-header').off('click.responsive').on('click.responsive', function() {
                // Mobilde daha hızlı animasyon
                const $content = $(this).next('.topic-content');
                if ($content.hasClass('active')) {
                    $content.css('max-height', $content[0].scrollHeight + 'px');
                }
            });
        }
    }

    // Sayfa yüklendiğinde ve pencere boyutu değiştiğinde çalıştır
    handleResponsiveAccordion();
    $(window).resize(handleResponsiveAccordion);

    // Kurs meta bilgilerini vurgula
    $('.meta-item.updated-date').hover(
        function() {
            $(this).css({
                'transform': 'translateY(-2px)',
                'box-shadow': '0 4px 8px rgba(40, 167, 69, 0.2)'
            });
        },
        function() {
            $(this).css({
                'transform': 'translateY(0)',
                'box-shadow': 'none'
            });
        }
    );

    // Eğitmen kartı hover efekti
    $('.instructor-card').hover(
        function() {
            $(this).css({
                'transform': 'translateY(-3px)',
                'box-shadow': '0 6px 20px rgba(0,0,0,0.15)'
            });
        },
        function() {
            $(this).css({
                'transform': 'translateY(0)',
                'box-shadow': '0 2px 4px rgba(0,0,0,0.1)'
            });
        }
    );

    // Sosyal medya linklerine tooltip ekle
    $('.social-link').each(function() {
        const platform = $(this).attr('class').match(/social-(\w+)/);
        if (platform && platform[1]) {
            const platformName = platform[1].charAt(0).toUpperCase() + platform[1].slice(1);
            $(this).attr('title', platformName + ' profilini görüntüle');
        }
    });

    // Kurs müfredatı öğelerini sayma ve güncelleme
    function updateCurriculumCounts() {
        $('.curriculum-topic').each(function() {
            const $topic = $(this);
            const lessonCount = $topic.find('.lesson-item').length;
            const quizCount = $topic.find('.quiz-item').length;
            const totalCount = lessonCount + quizCount;
            
            $topic.find('.topic-count').text(`(${totalCount} öğe)`);
        });
    }

    updateCurriculumCounts();

    // Kurs bilgileri sekmesi açıldığında animasyonları başlat
    if ($('.woo-pruduct-course-info').length > 0) {
        // Fade-in animasyonu
        $('.course-overview, .course-meta-info, .course-instructor-info, .course-curriculum').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateY(0)');
        });
    }
});
